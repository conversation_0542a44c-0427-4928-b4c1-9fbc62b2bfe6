// Zustand状态管理文件
// 此文件用于管理应用的全局状态

import { create } from 'zustand';

interface ButtonState {
  modeActive: boolean;
  businessActive: boolean;
  coordinateActive: boolean; // 坐标按键状态
  initializeTrigger: number; // 初始化触发器时间戳
  // 模式功能面板状态
  bottomColorActive: boolean; // 底色按键状态
  colorfulActive: boolean; // 彩色按键状态
  addTrigger: number; // 添加按键触发器时间戳
  initialActive: boolean; // 初始按键状态
  numberActive: boolean; // 数字按键状态
  setModeActive: () => void;
  setBusinessActive: () => void;
  setCoordinateActive: () => void;
  triggerInitialize: () => void;
  // 模式功能面板方法
  setBottomColorActive: () => void;
  setColorfulActive: () => void;
  triggerAdd: () => void;
  setInitialActive: () => void;
  setNumberActive: () => void;
}

export const useStore = create<ButtonState>((set) => ({
  modeActive: true, // 默认激活模式按键
  businessActive: false,
  coordinateActive: false, // 默认坐标按键未激活
  initializeTrigger: 0, // 初始化触发器时间戳
  // 模式功能面板状态初始化
  bottomColorActive: false, // 默认底色按键未激活
  colorfulActive: false, // 默认彩色按键未激活
  addTrigger: 0, // 添加按键触发器时间戳
  initialActive: false, // 默认初始按键未激活
  numberActive: false, // 默认数字按键未激活
  // 点击模式按键时，将其状态设置为true，并将业务按键状态设置为false
  setModeActive: () => set({ modeActive: true, businessActive: false }),
  // 点击业务按键时，将其状态设置为true，并将模式按键状态设置为false
  setBusinessActive: () => set({ businessActive: true, modeActive: false }),
  // 切换坐标按键状态
  setCoordinateActive: () => set((state) => ({ coordinateActive: !state.coordinateActive })),
  // 触发初始化事件，重置坐标按键状态并更新时间戳
  triggerInitialize: () => set({ coordinateActive: false, initializeTrigger: Date.now() }),
  // 模式功能面板方法
  // 切换底色按键状态
  setBottomColorActive: () => set((state) => ({ bottomColorActive: !state.bottomColorActive })),
  // 切换彩色按键状态
  setColorfulActive: () => set((state) => ({ colorfulActive: !state.colorfulActive })),
  // 触发添加事件，更新时间戳
  triggerAdd: () => set({ addTrigger: Date.now() }),
  // 切换初始按键状态
  setInitialActive: () => set((state) => ({ initialActive: !state.initialActive })),
  // 切换数字按键状态
  setNumberActive: () => set((state) => ({ numberActive: !state.numberActive })),
}));
