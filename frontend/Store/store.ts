// Zustand状态管理文件
// 此文件用于管理应用的全局状态

import { create } from 'zustand';

interface ButtonState {
  modeActive: boolean;
  businessActive: boolean;
  coordinateActive: boolean; // 坐标按键状态
  initializeTrigger: number; // 初始化触发器时间戳
  setModeActive: () => void;
  setBusinessActive: () => void;
  setCoordinateActive: () => void;
  triggerInitialize: () => void;
}

export const useStore = create<ButtonState>((set) => ({
  modeActive: true, // 默认激活模式按键
  businessActive: false,
  coordinateActive: false, // 默认坐标按键未激活
  initializeTrigger: 0, // 初始化触发器时间戳
  // 点击模式按键时，将其状态设置为true，并将业务按键状态设置为false
  setModeActive: () => set({ modeActive: true, businessActive: false }),
  // 点击业务按键时，将其状态设置为true，并将模式按键状态设置为false
  setBusinessActive: () => set({ businessActive: true, modeActive: false }),
  // 切换坐标按键状态
  setCoordinateActive: () => set((state) => ({ coordinateActive: !state.coordinateActive })),
  // 触发初始化事件，重置坐标按键状态并更新时间戳
  triggerInitialize: () => set({ coordinateActive: false, initializeTrigger: Date.now() }),
}));
