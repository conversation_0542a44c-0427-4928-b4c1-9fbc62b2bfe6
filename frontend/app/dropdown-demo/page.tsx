'use client';

import React from 'react';
import { DropdownButton } from '../../ButtonCodes';
import type { DropdownItem } from '../../ButtonCodes';

export default function DropdownDemoPage() {
  // 配置初始菜单项，按照文档要求
  const initialItems: DropdownItem[] = [
    {
      id: 'business1',
      name: '业务1',
      mode: 'button',
      isActive: true,
    },
    {
      id: 'business2', 
      name: '业务2',
      mode: 'button',
      isActive: false,
    },
    {
      id: 'add',
      name: '',
      mode: 'add',
      isActive: false,
    },
  ];

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      backgroundColor: '#ffffff',
    }}>
      <DropdownButton
        initialItems={initialItems}
      />
    </div>
  );
}
