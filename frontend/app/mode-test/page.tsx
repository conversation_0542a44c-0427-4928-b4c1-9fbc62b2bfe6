'use client';

import React from 'react';
import FeatureB1_2 from '../../features/feature/featureB1_2';
import { useStore } from '../../Store/store';

export default function ModeTestPage() {
  const { 
    bottomColorActive,
    colorfulActive,
    addTrigger,
    initialActive,
    coordinateActive,
    numberActive
  } = useStore();

  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>模式功能面板测试</h1>
      
      {/* 状态显示 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3>当前状态</h3>
        <ul>
          <li>底色按键状态: {bottomColorActive ? '激活' : '未激活'}</li>
          <li>彩色按键状态: {colorfulActive ? '激活' : '未激活'}</li>
          <li>添加触发器: {addTrigger}</li>
          <li>初始按键状态: {initialActive ? '激活' : '未激活'}</li>
          <li>坐标按键状态: {coordinateActive ? '激活' : '未激活'}</li>
          <li>数字按键状态: {numberActive ? '激活' : '未激活'}</li>
        </ul>
      </div>

      {/* 模式功能容器演示 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd'
      }}>
        <h3>模式功能容器测试</h3>
        <div style={{
          width: '400px',
          height: '150px',
          backgroundColor: '#6d6d6d',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'center',
          overflow: 'visible',
          position: 'relative',
          border: '2px solid #333'
        }}>
          <FeatureB1_2 />
        </div>
      </div>

      {/* 修复说明 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd',
        marginTop: '20px'
      }}>
        <h3>修复内容</h3>
        <ul>
          <li>✅ 按键大小：现在按键会根据容器尺寸自适应（35%高度 × 46%宽度）</li>
          <li>✅ 下拉菜单裁剪：修改了父容器的overflow属性为visible</li>
          <li>✅ 按键位置：严格按照文档要求定位（顶部55%，左右4%）</li>
          <li>✅ 文本大小：设置为自适应</li>
          <li>✅ z-index：设置为1000确保下拉菜单显示在最上层</li>
        </ul>
      </div>
    </div>
  );
}
