'use client';

import React from 'react';
import SecondaryButton from '../../ButtonCodes/secondary/SecondaryButton/SecondaryButton';
import { useStore } from '../../Store/store';
import { useLogicB1_1 } from '../logic/logicB1_1';

// 矩阵功能容器组件
const FeatureB1_1: React.FC = () => {
  const { coordinateActive, setCoordinateActive, triggerInitialize } = useStore();
  const { modeActive } = useLogicB1_1();

  // 初始化按键点击处理
  const handleInitializeClick = () => {
    triggerInitialize();
  };

  // 坐标按键点击处理
  const handleCoordinateClick = () => {
    setCoordinateActive();
  };

  return (
    <div style={{
      position: 'relative',
      height: '15%',
      width: '94%',
      backgroundColor: '#bebebe',
      overflow: 'hidden',
      top: '6%',
    }}>
      {/* 矩阵标题文本 */}
      <span style={{
        position: 'absolute',
        top: '20%',
        left: '1%',
        fontSize: '25px',
        color: '#242424',
        margin: 0,
        padding: 0,
      }}>
        矩阵
      </span>

      {/* 初始化按键 */}
      <SecondaryButton
        mode="instant"
        text="初始化"
        onClick={handleInitializeClick}
        style={{
          position: 'absolute',
          top: '55%',
          left: '4%',
          height: '35%',
          width: '44%',
          fontSize: 'auto',
        }}
      />

      {/* 坐标按键 */}
      <SecondaryButton
        mode="toggle"
        isActive={coordinateActive}
        text="坐标"
        onClick={handleCoordinateClick}
        style={{
          position: 'absolute',
          top: '55%',
          right: '4%',
          height: '35%',
          width: '44%',
          fontSize: 'auto',
        }}
      />
    </div>
  );
};

export default FeatureB1_1;
