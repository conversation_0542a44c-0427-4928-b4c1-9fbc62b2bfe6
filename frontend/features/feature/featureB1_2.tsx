'use client';

import React from 'react';
import { DropdownButton } from '../../ButtonCodes';
import type { DropdownItem } from '../../ButtonCodes';
import { useStore } from '../../Store/store';
import { useLogicB1_2 } from '../logic/logicB1_2';

// 模式功能容器组件
const FeatureB1_2: React.FC = () => {
  const {
    setBottomColorActive,
    setColorfulActive,
    triggerAdd,
    setInitialActive,
    setNumberActive,
    setCoordinateActive
  } = useStore();
  const {
    bottomColorActive,
    colorfulActive,
    initialActive,
    numberActive,
    coordinateActive
  } = useLogicB1_2();

  // 配置第一个下拉按键的菜单项（底色、彩色、添加）
  const firstDropdownItems: DropdownItem[] = [
    {
      id: 'bottom-color',
      name: '底色',
      mode: 'button',
      isActive: bottomColorActive,
    },
    {
      id: 'colorful',
      name: '彩色',
      mode: 'button',
      isActive: colorfulActive,
    },
    {
      id: 'add',
      name: '',
      mode: 'add',
      isActive: false,
    },
  ];

  // 配置第二个下拉按键的菜单项（初始、坐标、数字）
  const secondDropdownItems: DropdownItem[] = [
    {
      id: 'initial',
      name: '初始',
      mode: 'button',
      isActive: initialActive,
    },
    {
      id: 'coordinate',
      name: '坐标',
      mode: 'button',
      isActive: coordinateActive, // 坐标按键状态由store管理
    },
    {
      id: 'number',
      name: '数字',
      mode: 'button',
      isActive: numberActive,
    },
  ];

  // 处理第一个下拉按键的选择变化
  const handleFirstDropdownChange = (selectedItem: DropdownItem | null) => {
    if (selectedItem) {
      switch (selectedItem.id) {
        case 'bottom-color':
          setBottomColorActive();
          break;
        case 'colorful':
          setColorfulActive();
          break;
        case 'add':
          triggerAdd();
          break;
      }
    }
  };

  // 处理第二个下拉按键的选择变化
  const handleSecondDropdownChange = (selectedItem: DropdownItem | null) => {
    if (selectedItem) {
      switch (selectedItem.id) {
        case 'initial':
          setInitialActive();
          break;
        case 'coordinate':
          // 坐标按键的状态由store中的setCoordinateActive管理
          setCoordinateActive();
          break;
        case 'number':
          setNumberActive();
          break;
      }
    }
  };

  return (
    <div style={{
      position: 'relative',
      height: '15%',
      width: '94%',
      backgroundColor: '#bebebe',
      overflow: 'visible', // 修改为visible以避免裁剪下拉菜单
      top: '9%', // 根据文档要求，顶部对齐占componetB1容器高的9%
    }}>
      {/* 模式标题文本 */}
      <span style={{
        position: 'absolute',
        top: '20%', // 根据文档要求，顶部对齐featureB1_2高度的20%
        left: '1%', // 根据文档要求，左部对齐featureB1_2宽度的1%
        fontSize: '25px',
        color: '#242424',
        margin: 0,
        padding: 0,
      }}>
        模式
      </span>

      {/* 第一个下拉按键 */}
      <div style={{
        position: 'absolute',
        top: '55%', // 根据文档要求，顶部对齐featureB1_2高度的55%
        left: '4%', // 根据文档要求，左部对齐featureB1_2宽度的4%
        height: '35%', // 根据文档要求，键高占featureB1_2容器高的35%
        width: '46%', // 根据文档要求，键宽占featureB1_2容器宽的46%
        zIndex: 100, // 降低z-index，确保对话框在最上层
      }}>
        <div style={{
          height: '100%',
          width: '100%',
        }}>
          <DropdownButton
            initialItems={firstDropdownItems}
            onSelectionChange={handleFirstDropdownChange}
            style={{
              height: '100%',
              width: '100%',
              fontSize: 'auto', // 文本大小自适应
            }}
          />
        </div>
      </div>

      {/* 第二个下拉按键 */}
      <div style={{
        position: 'absolute',
        top: '55%', // 根据文档要求，顶部对齐featureB1_2高度的55%
        right: '4%', // 根据文档要求，右部对齐featureB1_2宽度的4%
        height: '35%', // 根据文档要求，键高占featureB1_2容器高的35%
        width: '46%', // 根据文档要求，键宽占featureB1_2容器宽的46%
        zIndex: 99, // 设置比按键1更低的z-index，确保对话框在最上层
      }}>
        <div style={{
          height: '100%',
          width: '100%',
        }}>
          <DropdownButton
            initialItems={secondDropdownItems}
            onSelectionChange={handleSecondDropdownChange}
            style={{
              height: '100%',
              width: '100%',
              fontSize: 'auto', // 文本大小自适应
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default FeatureB1_2;
