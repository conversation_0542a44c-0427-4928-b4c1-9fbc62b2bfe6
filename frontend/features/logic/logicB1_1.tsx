'use client';

import { useEffect } from 'react';
import { useStore } from '../../Store/store';

// 矩阵功能容器交互逻辑
export const useLogicB1_1 = () => {
  const { modeActive, coordinateActive, setCoordinateActive } = useStore();

  // 监听模式按键状态，当状态为false时，将坐标按键状态设置为false
  useEffect(() => {
    if (!modeActive) {
      // 如果坐标按键当前是激活状态，则将其设置为false
      if (coordinateActive) {
        setCoordinateActive();
      }
    }
  }, [modeActive, coordinateActive, setCoordinateActive]);

  return {
    modeActive,
    coordinateActive,
  };
};

export default useLogicB1_1;
