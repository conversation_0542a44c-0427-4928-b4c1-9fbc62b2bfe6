'use client';

import { useEffect } from 'react';
import { useStore } from '../../Store/store';

// 模式功能容器交互逻辑
export const useLogicB1_2 = () => {
  const { 
    modeActive, 
    coordinateActive, 
    setCoordinateActive,
    bottomColorActive,
    colorfulActive,
    addTrigger,
    initialActive,
    numberActive
  } = useStore();

  // 监听模式按键状态，当状态为false时，将坐标按键状态设置为false
  useEffect(() => {
    if (!modeActive) {
      // 如果坐标按键当前是激活状态，则将其设置为false
      if (coordinateActive) {
        setCoordinateActive();
      }
    }
  }, [modeActive, coordinateActive, setCoordinateActive]);

  return {
    modeActive,
    coordinateActive,
    bottomColorActive,
    colorfulActive,
    addTrigger,
    initialActive,
    numberActive
  };
};

export default useLogicB1_2;
