import React from 'react';
import ComponetB1 from '../componet/componetB1';
import ComponetB2 from '../componet/componetB2';
import ComponetButton from '../componet/componetButton';
import { useStore } from '../../Store/store';

const ComponetInteractionB: React.FC = () => {
  const { modeActive } = useStore();

  return (
    <div style={{
      height: '99vh',
      width: '20vw',
      position: 'relative',
      marginLeft: '1vw'
    }}>
      {/* 始终显示的按键容器 */}
      <ComponetButton />
      
      {/* 根据状态切换显示的容器 */}
      {modeActive ? (
        <ComponetB1 />
      ) : (
        <ComponetB2 />
      )}
    </div>
  );
};

export default ComponetInteractionB;
