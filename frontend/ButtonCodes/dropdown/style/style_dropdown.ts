// 下拉按键样式定义
export const dropdownStyles = {
  // 主按键样式
  button: {
    name: 'dropdown',
    shape: 'rectangle',
    height: '50px',
    width: '200px',
    borderRadius: '0px',
    backgroundColor: '#f1f1f1',
    border: 'none', // 移除默认边框
    outline: 'none', // 移除焦点轮廓
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    position: 'relative' as const,
  },

  // 下拉菜单样式
  menu: {
    shape: 'rectangle',
    width: '200px', // 等于按键宽度
    maxHeight: '300px',
    overflowY: 'auto' as const,
    position: 'absolute' as const,
    top: '100%',
    left: '0',
    backgroundColor: '#ffffff',
    border: '1px solid #cccccc',
    display: 'none', // 默认隐藏
    zIndex: 500, // 降低z-index，确保对话框在最上层
  },

  // 文字样式
  text: {
    defaultText: '按键',
    fontSize: '20px',
    color: '#242424',
    textAlign: 'center' as const,
  },

  // 图标样式
  icon: {
    type: 'arrow-down',
    position: 'absolute' as const,
    right: '10px',
    size: '20px',
    color: '#cccccc',
  },

  // 菜单项样式
  menuItem: {
    // 默认模式按键
    button: {
      textColor: '#2d2d2d',
      fontSize: '15px',
      textAlign: 'center' as const,
      padding: '10px',
      cursor: 'pointer',
      backgroundColor: '#f1f1f1',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },

    // 新建模式按键
    add: {
      iconType: '+',
      iconSize: '15px',
      iconColor: '#cccccc',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '10px',
      cursor: 'pointer',
      backgroundColor: '#f1f1f1',
    },

    // 存储按键
    storage: {
      textColor: '#2d2d2d',
      fontSize: '15px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '10px',
      cursor: 'pointer',
      backgroundColor: '#f1f1f1',
      position: 'relative' as const,
      deleteIcon: {
        type: '-',
        color: '#0d0d0d',
        position: 'absolute' as const,
        right: '20px',
        size: '15px',
      },
    },
  },

  // 对话框弹窗样式
  dialog: {
    // 遮罩层
    overlay: {
      position: 'fixed' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      zIndex: 9999,
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    },

    // 对话框
    modal: {
      backgroundColor: '#ffffff',
      height: '250px',
      width: '400px',
      borderRadius: '10px',
      position: 'relative' as const,
      display: 'flex',
      flexDirection: 'column' as const,
      justifyContent: 'center',
      alignItems: 'center',
    },

    // 关闭图标
    closeIcon: {
      type: 'x',
      color: '#0d0d0d',
      position: 'absolute' as const,
      top: '20px',
      right: '20px',
      size: '40px',
      cursor: 'pointer',
    },

    // 输入框区域
    input: {
      label: {
        text: '名称',
        fontSize: '25px',
        color: '#9f9f9f',
        marginBottom: '5px',
        alignSelf: 'flex-start',
        marginLeft: '25px',
      },
      field: {
        height: '50px',
        width: '350px',
        backgroundColor: '#ffffff',
        border: '2px solid #9f9f9f',
        borderRadius: '5px',
        color: '#9f9f9f',
        fontSize: '20px',
        paddingLeft: '5px',
        cursor: 'text',
      },
      error: {
        position: 'absolute' as const,
        bottom: '50px',
        fontSize: '20px',
        color: '#ff0000',
        opacity: 0.5,
        display: 'none',
        textAlign: 'center' as const,
        width: '100%',
      },
    },

    // 确认按键
    confirmButton: {
      text: '确认',
      color: '#ffffff',
      height: '30px',
      width: '80px',
      backgroundColor: '#0d0d0d',
      position: 'absolute' as const,
      bottom: '25px',
      right: '25px',
      borderRadius: '5px',
      border: 'none',
      cursor: 'pointer',
    },
  },

  // 交互状态样式
  hover: {
    menuItem: {
      button: {
        active: {
          default: '#929292',
          hover: '#b6b6b6',
        },
        inactive: {
          default: '#f1f1f1',
          hover: '#dbdbdb',
        },
      },
      add: {
        hover: '#dbdbdb',
        mouseDown: '#929292',
        mouseUp: '#f1f1f1',
      },
      storage: {
        active: {
          default: '#929292',
          hover: '#b6b6b6',
        },
        inactive: {
          default: '#f1f1f1',
          hover: '#dbdbdb',
        },
        deleteIcon: {
          hover: '#5e5e5e',
          scale: 2,
        },
      },
    },
    dialog: {
      closeIcon: {
        hover: '#5e5e5e',
      },
      confirmButton: {
        hover: '#5e5e5e',
      },
    },
  },
};

export type DropdownStyles = typeof dropdownStyles;
