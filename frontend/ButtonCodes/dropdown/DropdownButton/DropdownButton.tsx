'use client';

import React, { useState, useRef, useEffect } from 'react';
import { dropdownStyles } from '../style/style_dropdown';
import DropdownEventManager, { DropdownItem } from '../event/event_dropdown';

export interface DropdownButtonProps {
  mode?: 'button' | 'add' | 'storage';
  initialItems?: DropdownItem[];
  onSelectionChange?: (selectedItem: DropdownItem | null) => void;
}

const DropdownButton: React.FC<DropdownButtonProps> = ({
  mode = 'button',
  initialItems = [
    { id: 'default', name: '按键', mode: 'button', isActive: true },
    { id: 'add', name: '', mode: 'add', isActive: false },
  ],
  onSelectionChange,
}) => {
  const [eventManager] = useState(() => new DropdownEventManager(initialItems));
  const [state, setState] = useState(eventManager.getState());
  const [inputValue, setInputValue] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [hoveredDeleteIcon, setHoveredDeleteIcon] = useState<string | null>(null);
  const [hoveredCloseIcon, setHoveredCloseIcon] = useState(false);
  const [hoveredConfirmButton, setHoveredConfirmButton] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 更新状态
  const updateState = () => {
    const newState = eventManager.getState();
    setState(newState);
    
    // 通知父组件选择变化
    if (onSelectionChange) {
      const activeItem = newState.items.find(item => item.isActive);
      onSelectionChange(activeItem || null);
    }
  };

  // 处理点击外部区域
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      eventManager.handleClickOutside(event, dropdownRef.current);
      updateState();
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理主按键点击
  const handleMainButtonClick = () => {
    eventManager.toggleMenu();
    updateState();
  };

  // 处理菜单项点击
  const handleMenuItemClick = (item: DropdownItem) => {
    if (item.mode === 'add') {
      eventManager.showDialog();
      updateState();
      // 聚焦输入框
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    } else {
      eventManager.selectItem(item.id);
      updateState();
    }
  };

  // 处理删除按键点击
  const handleDeleteClick = (e: React.MouseEvent, itemId: string) => {
    e.stopPropagation();
    eventManager.deleteItem(itemId);
    updateState();
  };

  // 处理对话框确认
  const handleConfirm = () => {
    const validation = eventManager.validateName(inputValue);
    if (!validation.isValid) {
      setErrorMessage(validation.error || '');
      // 3秒后隐藏错误信息
      setTimeout(() => setErrorMessage(''), 3000);
      return;
    }

    const result = eventManager.addItem(inputValue);
    if (result.success) {
      setInputValue('');
      setErrorMessage('');
      updateState();
    } else {
      setErrorMessage(result.error || '');
      setTimeout(() => setErrorMessage(''), 3000);
    }
  };

  // 处理对话框关闭
  const handleCloseDialog = () => {
    eventManager.hideDialog();
    setInputValue('');
    setErrorMessage('');
    updateState();
  };

  // 处理输入框回车
  const handleInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleConfirm();
    }
  };

  return (
    <div ref={dropdownRef} style={{ position: 'relative', display: 'inline-block' }}>
      {/* 主按键 */}
      <button
        style={dropdownStyles.button}
        onClick={handleMainButtonClick}
      >
        <span style={dropdownStyles.text}>
          {eventManager.getActiveItemName()}
        </span>
        <span style={dropdownStyles.icon}>▼</span>
      </button>

      {/* 下拉菜单 */}
      {state.isMenuOpen && (
        <div style={{
          ...dropdownStyles.menu,
          display: 'block',
        }}>
          {state.items.map((item) => (
            <div
              key={item.id}
              style={{
                ...dropdownStyles.menuItem[item.mode],
                ...eventManager.getHoverStyle(item, hoveredItem === item.id),
              }}
              onMouseEnter={() => setHoveredItem(item.id)}
              onMouseLeave={() => setHoveredItem(null)}
              onClick={() => handleMenuItemClick(item)}
            >
              {item.mode === 'add' ? (
                <span style={{ fontSize: dropdownStyles.menuItem.add.iconSize }}>+</span>
              ) : (
                <>
                  <span>{item.name}</span>
                  {item.mode === 'storage' && (
                    <span
                      style={{
                        ...dropdownStyles.menuItem.storage.deleteIcon,
                        ...eventManager.getDeleteIconHoverStyle(hoveredDeleteIcon === item.id),
                      }}
                      onMouseEnter={() => setHoveredDeleteIcon(item.id)}
                      onMouseLeave={() => setHoveredDeleteIcon(null)}
                      onClick={(e) => handleDeleteClick(e, item.id)}
                    >
                      -
                    </span>
                  )}
                </>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 对话框 */}
      {state.isDialogOpen && (
        <div style={dropdownStyles.dialog.overlay}>
          <div style={dropdownStyles.dialog.modal}>
            {/* 关闭图标 */}
            <span
              style={{
                ...dropdownStyles.dialog.closeIcon,
                color: hoveredCloseIcon ? '#5e5e5e' : '#0d0d0d',
              }}
              onMouseEnter={() => setHoveredCloseIcon(true)}
              onMouseLeave={() => setHoveredCloseIcon(false)}
              onClick={handleCloseDialog}
            >
              ×
            </span>

            {/* 输入区域 */}
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <label style={dropdownStyles.dialog.input.label}>名称</label>
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleInputKeyPress}
                style={dropdownStyles.dialog.input.field}
              />
            </div>

            {/* 错误信息 */}
            {errorMessage && (
              <div style={{
                ...dropdownStyles.dialog.input.error,
                display: 'block',
              }}>
                {errorMessage}
              </div>
            )}

            {/* 确认按键 */}
            <button
              style={{
                ...dropdownStyles.dialog.confirmButton,
                backgroundColor: hoveredConfirmButton ? '#5e5e5e' : '#0d0d0d',
              }}
              onMouseEnter={() => setHoveredConfirmButton(true)}
              onMouseLeave={() => setHoveredConfirmButton(false)}
              onClick={handleConfirm}
            >
              确认
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DropdownButton;
