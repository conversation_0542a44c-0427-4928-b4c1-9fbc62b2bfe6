// 下拉按键事件处理

export interface DropdownItem {
  id: string;
  name: string;
  mode: 'button' | 'add' | 'storage';
  isActive: boolean;
}

export interface DropdownEventHandlers {
  onToggleMenu: () => void;
  onSelectItem: (itemId: string) => void;
  onAddItem: (name: string) => void;
  onDeleteItem: (itemId: string) => void;
  onCloseDialog: () => void;
  onShowDialog: () => void;
}

export class DropdownEventManager {
  private isMenuOpen: boolean = false;
  private isDialogOpen: boolean = false;
  private items: DropdownItem[] = [];
  private activeItemId: string | null = null;

  constructor(initialItems: DropdownItem[] = []) {
    this.items = initialItems;
    // 设置默认激活项
    const firstItem = this.items.find(item => item.mode === 'button');
    if (firstItem) {
      this.activeItemId = firstItem.id;
      firstItem.isActive = true;
    }
  }

  // 切换菜单显示/隐藏
  toggleMenu(): boolean {
    this.isMenuOpen = !this.isMenuOpen;
    return this.isMenuOpen;
  }

  // 隐藏菜单
  hideMenu(): void {
    this.isMenuOpen = false;
  }

  // 显示对话框
  showDialog(): void {
    this.isDialogOpen = true;
  }

  // 隐藏对话框
  hideDialog(): void {
    this.isDialogOpen = false;
  }

  // 选择菜单项
  selectItem(itemId: string): void {
    // 互斥单选：取消所有项的激活状态
    this.items.forEach(item => {
      item.isActive = false;
    });

    // 激活选中的项
    const selectedItem = this.items.find(item => item.id === itemId);
    if (selectedItem && selectedItem.mode !== 'add') {
      selectedItem.isActive = true;
      this.activeItemId = itemId;
    }

    // 隐藏菜单
    this.hideMenu();
  }

  // 添加新项
  addItem(name: string): { success: boolean; error?: string } {
    // 验证名称
    if (!name.trim()) {
      return { success: false, error: '名称不能为空' };
    }

    // 检查重复
    const isDuplicate = this.items.some(item => 
      item.name === name && item.mode !== 'add'
    );
    if (isDuplicate) {
      return { success: false, error: '名称不能与现有名称重复' };
    }

    // 创建新项
    const newItem: DropdownItem = {
      id: `storage_${Date.now()}`,
      name: name,
      mode: 'storage',
      isActive: false,
    };

    // 找到插入位置（在add按键之前）
    const addIndex = this.items.findIndex(item => item.mode === 'add');
    if (addIndex !== -1) {
      this.items.splice(addIndex, 0, newItem);
    } else {
      this.items.push(newItem);
    }

    // 激活新创建的项
    this.selectItem(newItem.id);

    // 隐藏对话框
    this.hideDialog();

    return { success: true };
  }

  // 删除项
  deleteItem(itemId: string): void {
    const itemIndex = this.items.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    const itemToDelete = this.items[itemIndex];
    
    // 不能删除默认模式按键
    if (itemToDelete.mode === 'button') return;

    // 删除项
    this.items.splice(itemIndex, 1);

    // 如果删除的是激活项，需要重新选择激活项
    if (itemToDelete.isActive) {
      const firstButton = this.items.find(item => item.mode === 'button');
      if (firstButton) {
        this.selectItem(firstButton.id);
      }
    }
  }

  // 获取当前状态
  getState() {
    return {
      isMenuOpen: this.isMenuOpen,
      isDialogOpen: this.isDialogOpen,
      items: [...this.items],
      activeItemId: this.activeItemId,
    };
  }

  // 获取激活项名称
  getActiveItemName(): string {
    const activeItem = this.items.find(item => item.id === this.activeItemId);
    return activeItem ? activeItem.name : '按键';
  }

  // 检查是否应该显示add按键
  shouldShowAddButton(): boolean {
    return this.items.some(item => item.mode === 'button');
  }

  // 处理点击外部区域
  handleClickOutside(event: MouseEvent, dropdownRef: HTMLElement | null): void {
    if (!dropdownRef) return;

    // 如果对话框打开，不隐藏菜单
    if (this.isDialogOpen) return;

    // 如果点击在下拉组件外部，隐藏菜单
    if (!dropdownRef.contains(event.target as Node)) {
      this.hideMenu();
    }
  }

  // 验证输入名称
  validateName(name: string): { isValid: boolean; error?: string } {
    if (!name.trim()) {
      return { isValid: false, error: '名称不能为空' };
    }

    const isDuplicate = this.items.some(item => 
      item.name === name && item.mode !== 'add'
    );
    if (isDuplicate) {
      return { isValid: false, error: '名称不能与现有名称重复' };
    }

    return { isValid: true };
  }

  // 获取悬停样式
  getHoverStyle(item: DropdownItem, isHovered: boolean): React.CSSProperties {
    if (!isHovered) {
      return item.isActive ? 
        { backgroundColor: '#929292' } : 
        { backgroundColor: '#f1f1f1' };
    }

    switch (item.mode) {
      case 'button':
      case 'storage':
        return item.isActive ? 
          { backgroundColor: '#b6b6b6' } : 
          { backgroundColor: '#dbdbdb' };
      case 'add':
        return { backgroundColor: '#dbdbdb' };
      default:
        return {};
    }
  }

  // 获取删除图标悬停样式
  getDeleteIconHoverStyle(isHovered: boolean): React.CSSProperties {
    return isHovered ? 
      { color: '#5e5e5e', transform: 'scale(2)' } : 
      { color: '#0d0d0d', transform: 'scale(1)' };
  }
}

export default DropdownEventManager;
