#!/usr/bin/env node

/**
 * 普通按键功能测试脚本
 * 用于验证 SecondaryButton 组件的功能正确性
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 开始测试普通按键功能...\n');

try {
  // 检查是否安装了必要的测试依赖
  console.log('📦 检查测试依赖...');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = require(packageJsonPath);
  
  const requiredDeps = [
    '@testing-library/react',
    '@testing-library/jest-dom',
    '@testing-library/user-event',
    'jest'
  ];
  
  const missingDeps = requiredDeps.filter(dep => 
    !packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]
  );
  
  if (missingDeps.length > 0) {
    console.log('⚠️  缺少测试依赖，需要安装：', missingDeps.join(', '));
    console.log('请运行以下命令安装依赖：');
    console.log(`npm install --save-dev ${missingDeps.join(' ')}`);
    process.exit(1);
  }
  
  console.log('✅ 测试依赖检查完成\n');
  
  // 运行测试
  console.log('🚀 运行 SecondaryButton 组件测试...');
  
  const testCommand = 'npm test -- --testPathPattern=SecondaryButton.test.tsx --verbose';
  
  try {
    const output = execSync(testCommand, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log('✅ 测试执行完成！');
    console.log('\n📊 测试结果：');
    console.log(output);
    
  } catch (error) {
    console.log('❌ 测试执行失败');
    console.log('\n📊 测试输出：');
    console.log(error.stdout || error.message);
    
    if (error.stderr) {
      console.log('\n🚨 错误信息：');
      console.log(error.stderr);
    }
    
    process.exit(1);
  }
  
} catch (error) {
  console.error('❌ 测试脚本执行失败：', error.message);
  process.exit(1);
}

console.log('\n🎉 普通按键功能测试完成！');
