/**
 * 普通按键组件使用示例
 * 演示如何使用 SecondaryButton 组件
 */

import React, { useState } from 'react';
import { SecondaryButton } from '../ButtonCodes';

// 示例组件
export const SecondaryButtonExample: React.FC = () => {
  const [toggleState, setToggleState] = useState(false);
  const [clickCount, setClickCount] = useState(0);

  return (
    <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '20px' }}>
      <h2>普通按键组件示例</h2>
      
      {/* 默认持续状态模式按键 */}
      <div>
        <h3>1. 默认持续状态模式（Toggle）</h3>
        <SecondaryButton 
          text="切换按键"
          onClick={() => console.log('Toggle button clicked!')}
        />
      </div>
      
      {/* 受控的持续状态模式按键 */}
      <div>
        <h3>2. 受控持续状态模式</h3>
        <SecondaryButton 
          mode="toggle"
          isActive={toggleState}
          text={toggleState ? "已激活" : "未激活"}
          onClick={() => setToggleState(!toggleState)}
        />
        <p>当前状态: {toggleState ? '激活' : '未激活'}</p>
      </div>
      
      {/* 瞬时状态模式按键 */}
      <div>
        <h3>3. 瞬时状态模式（Instant）</h3>
        <SecondaryButton 
          mode="instant"
          text="瞬时按键"
          onClick={() => {
            setClickCount(prev => prev + 1);
            console.log('Instant button clicked!');
          }}
        />
        <p>点击次数: {clickCount}</p>
      </div>
      
      {/* 自定义样式按键 */}
      <div>
        <h3>4. 自定义样式按键</h3>
        <SecondaryButton 
          text="自定义样式"
          style={{
            width: '300px',
            height: '60px',
            borderRadius: '10px',
          }}
          onClick={() => console.log('Custom styled button clicked!')}
        />
      </div>
      
      {/* 自定义类名按键 */}
      <div>
        <h3>5. 带自定义类名的按键</h3>
        <SecondaryButton 
          text="自定义类名"
          className="my-custom-button"
          onClick={() => console.log('Custom class button clicked!')}
        />
      </div>
    </div>
  );
};

export default SecondaryButtonExample;
