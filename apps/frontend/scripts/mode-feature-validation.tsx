'use client';

import React from 'react';
import FeatureB1_2 from '../../../frontend/features/feature/featureB1_2';
import { useStore } from '../../../frontend/Store/store';

const ModeFeatureValidation: React.FC = () => {
  const { 
    modeActive,
    bottomColorActive,
    colorfulActive,
    addTrigger,
    initialActive,
    coordinateActive,
    numberActive
  } = useStore();

  return (
    <div style={{
      padding: '20px',
      backgroundColor: '#f5f5f5',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>模式功能面板验证</h1>
      
      {/* 状态显示 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd',
        marginBottom: '20px'
      }}>
        <h3>当前状态</h3>
        <ul>
          <li>模式按键状态: {modeActive ? '激活' : '未激活'}</li>
          <li>底色按键状态: {bottomColorActive ? '激活' : '未激活'}</li>
          <li>彩色按键状态: {colorfulActive ? '激活' : '未激活'}</li>
          <li>添加触发器: {addTrigger}</li>
          <li>初始按键状态: {initialActive ? '激活' : '未激活'}</li>
          <li>坐标按键状态: {coordinateActive ? '激活' : '未激活'}</li>
          <li>数字按键状态: {numberActive ? '激活' : '未激活'}</li>
        </ul>
      </div>

      {/* 模式功能容器演示 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd'
      }}>
        <h3>模式功能容器</h3>
        <div style={{
          width: '400px',
          height: '200px',
          backgroundColor: '#6d6d6d',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'center',
          overflow: 'hidden',
          position: 'relative'
        }}>
          <FeatureB1_2 />
        </div>
      </div>

      {/* 功能说明 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd',
        marginTop: '20px'
      }}>
        <h3>功能说明</h3>
        <ul>
          <li>模式容器：显示"模式"标题，包含两个下拉按键</li>
          <li>第一个下拉按键：包含底色、彩色、添加按键选项</li>
          <li>第二个下拉按键：包含初始、坐标、数字按键选项</li>
          <li>状态管理：所有按键状态都存储在Zustand store中</li>
          <li>添加按键：点击后触发添加事件，更新时间戳</li>
          <li>坐标按键：状态受模式按键影响，当模式为false时自动设为false</li>
        </ul>
      </div>
    </div>
  );
};

export default ModeFeatureValidation;
