/**
 * 矩阵功能面板验证脚本
 * 用于验证矩阵功能组件的基本功能
 */

import React from 'react';
import FeatureB1_1 from '../../../frontend/features/feature/featureB1_1';
import { useStore } from '../../../frontend/Store/store';

// 矩阵功能验证组件
export const MatrixFeatureValidation: React.FC = () => {
  const { coordinateActive, modeActive, initializeTrigger } = useStore();

  return (
    <div style={{ 
      padding: '20px', 
      display: 'flex', 
      flexDirection: 'column', 
      gap: '20px',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h2>矩阵功能面板验证</h2>
      
      {/* 状态显示 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd'
      }}>
        <h3>当前状态</h3>
        <p>模式按键状态: {modeActive ? '激活' : '未激活'}</p>
        <p>坐标按键状态: {coordinateActive ? '激活' : '未激活'}</p>
        <p>初始化触发器: {initializeTrigger}</p>
      </div>

      {/* 矩阵功能容器演示 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd'
      }}>
        <h3>矩阵功能容器</h3>
        <div style={{
          width: '400px',
          height: '200px',
          backgroundColor: '#6d6d6d',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'flex-start',
          alignItems: 'center',
          overflow: 'hidden',
          position: 'relative'
        }}>
          <FeatureB1_1 />
        </div>
      </div>

      {/* 功能说明 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'white', 
        borderRadius: '5px',
        border: '1px solid #ddd'
      }}>
        <h3>功能说明</h3>
        <ul>
          <li>矩阵容器：显示"矩阵"标题，包含初始化和坐标按键</li>
          <li>初始化按键：点击后重置坐标按键状态并更新时间戳</li>
          <li>坐标按键：切换状态，用于控制坐标显示</li>
          <li>状态监听：当模式按键为false时，坐标按键自动设为false</li>
        </ul>
      </div>
    </div>
  );
};

export default MatrixFeatureValidation;
