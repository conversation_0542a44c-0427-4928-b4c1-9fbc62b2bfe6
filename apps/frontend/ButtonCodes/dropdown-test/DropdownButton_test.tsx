'use client';

import React from 'react';
import { DropdownButton } from '../../../../frontend/ButtonCodes';
import type { DropdownItem } from '../../../../frontend/ButtonCodes';

const DropdownButtonTest: React.FC = () => {
  // 配置初始菜单项
  const initialItems: DropdownItem[] = [
    {
      id: 'business1',
      name: '业务1',
      mode: 'button',
      isActive: true,
    },
    {
      id: 'business2', 
      name: '业务2',
      mode: 'button',
      isActive: false,
    },
    {
      id: 'add',
      name: '',
      mode: 'add',
      isActive: false,
    },
  ];

  // 处理选择变化
  const handleSelectionChange = (selectedItem: DropdownItem | null) => {
    console.log('选中项:', selectedItem);
  };

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
      padding: '20px',
    }}>
      <DropdownButton
        initialItems={initialItems}
        onSelectionChange={handleSelectionChange}
      />
    </div>
  );
};

export default DropdownButtonTest;
