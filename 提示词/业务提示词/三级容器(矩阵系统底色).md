# 三级容器(矩阵系统底色)

## 底色偏移

**1.组件底色填充(featureA1_color.tsx):**

- 1.定义B组常量对象(group_b)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘16，0’
    - 1.中心编号‘B’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘0,0’
    - 2.橙色(一级坐标):‘8,8’
    - 3.黄色(一级坐标):'16,16'
    - 4.绿色(一级坐标):'24,0'
    - 5.青色(一级坐标):'32,0'
    - 6.蓝色(一级坐标):'24,0'
    - 7.紫色(一级坐标):'16,-16'
    - 8.粉色(一级坐标):'8,-8'
- 2.定义C组常量对象(group_c)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘-16，0’
    - 1.中心编号‘C’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘32,0’
    - 2.橙色(一级坐标):‘-24,8’
    - 3.黄色(一级坐标):'-16,16'
    - 4.绿色(一级坐标):'-8,8'
    - 5.青色(一级坐标):'0,0'
    - 6.蓝色(一级坐标):'-8,-8'
    - 7.紫色(一级坐标):'-16,-16'
    - 8.粉色(一级坐标):'-24,-8'
- 3.定义D组常量对象(group_d)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘0,16’
    - 1.中心编号‘D’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘-16,16’
    - 2.橙色(一级坐标):‘-8,24’
    - 3.黄色(一级坐标):'0,32'
    - 4.绿色(一级坐标):'8,24'
    - 5.青色(一级坐标):'16,16'
    - 6.蓝色(一级坐标):'8,8'
    - 7.紫色(一级坐标):'0,0'
    - 8.粉色(一级坐标):'-8,8'
- 4.定义E组常量对象(group_e)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘0,-16’
    - 1.中心编号‘E’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘-16,-16’
    - 2.橙色(一级坐标):‘-8,-8’
    - 3.黄色(一级坐标):'0,-16'
    - 4.绿色(一级坐标):'8,-8'
    - 5.青色(一级坐标):'16,-16'
    - 6.蓝色(一级坐标):'8,-24'
    - 7.紫色(一级坐标):'-32,0'
    - 8.粉色(一级坐标):'-8,-24'
- 5.定义F组常量对象(group_f)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘8,8’
    - 1.中心编号‘F’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘0,0’
    - 2.橙色(一级坐标):‘8,8’
    - 3.黄色(一级坐标):'16,16'
    - 4.绿色(一级坐标):'16,16'
    - 5.青色(一级坐标):'16,16'
    - 6.蓝色(一级坐标):'8,8'
    - 7.紫色(一级坐标):'0,0'
    - 8.粉色(一级坐标):'0,0'
- 6.定义G组常量对象(group_g)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘-8,-8’
    - 1.中心编号‘G’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘-16,-16’
    - 2.橙色(一级坐标):‘-8,-8’
    - 3.黄色(一级坐标):'0,0'
    - 4.绿色(一级坐标):'00'
    - 5.青色(一级坐标):'0,0'
    - 6.蓝色(一级坐标):'-8,-8'
    - 7.紫色(一级坐标):'-16,-16'
    - 8.粉色(一级坐标):'-16-16'
- 7.定义H组常量对象(group_h)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘8,-8’
    - 1.中心编号‘H’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘0,0’
    - 2.橙色(一级坐标):‘0,0’
    - 3.黄色(一级坐标):'0,0'
    - 4.绿色(一级坐标):'8,-8'
    - 5.青色(一级坐标):'16,-16'
    - 6.蓝色(一级坐标):'16,-16'
    - 7.紫色(一级坐标):'16,-16'
    - 8.粉色(一级坐标):'8,-8'
- 8.定义I组常量对象(group_i)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘-8,8’
    - 1.中心编号‘I’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘-16,16’
    - 2.橙色(一级坐标):‘-16,16’
    - 3.黄色(一级坐标):'-16,16'
    - 4.绿色(一级坐标):'-8,8'
    - 5.青色(一级坐标):'0,0'
    - 6.蓝色(一级坐标):'0,0'
    - 7.紫色(一级坐标):'0,0'
    - 8.粉色(一级坐标):'-8,8'
- 9.定义J组常量对象(group_j)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘16,16’
    - 1.中心编号‘J’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘16,16’
    - 2.橙色(一级坐标):‘16,16’
    - 3.黄色(一级坐标):'16,16'
    - 4.绿色(一级坐标):'16,16'
    - 5.青色(一级坐标):'16,16'
    - 6.蓝色(一级坐标):'16,16'
    - 7.紫色(一级坐标):'16,16'
    - 8.粉色(一级坐标):'16,16'
- 10.定义K组常量对象(group_k)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘-16,-16’
    - 1.中心编号‘K’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘-16,-16’
    - 2.橙色(一级坐标):‘-16,-16’
    - 3.黄色(一级坐标):'-16,-16'
    - 4.绿色(一级坐标):'-16,-16'
    - 5.青色(一级坐标):'-16,-16'
    - 6.蓝色(一级坐标):'-16,-16'
    - 7.紫色(一级坐标):'-16,-16'
    - 8.粉色(一级坐标):'-16,-16'
- 11.定义L组常量对象(group_l)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘16,-16’
    - 1.中心编号‘L’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘16,-16’
    - 2.橙色(一级坐标):‘16,-16’
    - 3.黄色(一级坐标):'16,-16'
    - 4.绿色(一级坐标):'16,-16'
    - 5.青色(一级坐标):'16,-16'
    - 6.蓝色(一级坐标):'16,-16'
    - 7.紫色(一级坐标):'16,-16'
    - 8.粉色(一级坐标):'16,-16'
- 12.定义M组常量对象(group_m)管理颜色坐标
  - 1.基于A组(group_a)进行整体偏移‘-16,16’
    - 1.中心编号‘M’
  - 2.一级坐标偏移
    - 1.红色(一级坐标):‘-16,16’
    - 2.橙色(一级坐标):‘-16,16’
    - 3.黄色(一级坐标):'-16,16'
    - 4.绿色(一级坐标):'-16,16'
    - 5.青色(一级坐标):'-16,16'
    - 6.蓝色(一级坐标):'-16,16'
    - 7.紫色(一级坐标):'-16,16'
    - 8.粉色(一级坐标):'-16,16'