# 三级容器(矩阵系统交互)

**1.客户端组件:**

- 1.渲染指令:'use client'

## 组件交互逻辑

**1.状态管理(store.ts):**

- 1.记录状态
  - 1.记录存储被激活的组件(featureA1)坐标

**2.业务隔离(logicA1.tsx):**

- 1.监听‘store.ts’中的‘模式’按键状态('true'/'false')
  - 1.状态为‘true’时:
    - 1.启用'logicA1.tsx'逻辑
    - 2.状态获取
  - 2.从'store.ts'获取被激活的组件坐标
    - 1.根据坐标复原组件状态
  - 3.状态为‘false’时:
    - 1.禁用'logicA1.tsx'逻辑
    - 2.组件状态:
      - 1.组件激活状态:全部设置为‘false’
      - 2.组件高亮边框:全部清除

**3.状态初始化(logicA1.tsx):**

- 1.监听'store.ts'中的‘初始化按键’(一次性事件)
  - 1.‘初始化’事件(时间戳变化)
- 2.‘初始化’事件触发:
  - 1.将所有‘featureA1’组件的‘激活’状态设置为‘false’
  - 2.清除所有组件的‘高亮边框’

**4.坐标显示(logicA1.tsx):**

- 1.访问‘store.ts’中‘坐标按键’的状态('true'/'false')
- 2.状态为‘true’时:
  - 1.计算每个组件的坐标 (以中心组件为原点 '0,0')
  - 2.将坐标字符串(如: '-1,0','1,0')作为文本显示在对应组件内部
- 3.状态为‘false’时:
  - 1.隐藏所有组件坐标文本

**5.组件点击(logicA1.tsx):**

- 1.激活状态(独立开关)
  - 1.点击后切换并保持‘激活’/‘未激活’状态
    - 1.'激活'状态
      - 1.背景颜色:坐标底色
    - 2.'未激活'状态
      - 1.背景颜色:#ffffff
  - 2.此状态不改变其他组件状态
- 2.高亮边框(互斥单选)
  - 1.点击任意组件产生高亮边框
    - 1.移除其他组件的高亮边框
  - 2.高亮边框样式
    - 1.边框宽度:3px
    - 2.边框颜色:#ffd500
  - 3.避免布局偏移
    - 1.边框不影响元素尺寸

**6.鼠标悬停(logicA1.tsx):**

- 1.样式:
  - 1.组件: 悬浮前置
  - 2.组件大小:放大1.2倍
