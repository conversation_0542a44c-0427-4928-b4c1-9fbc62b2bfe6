# 三级容器(模式)

## 状态初始化

**1.状态监听(logicB1_2):**

- 1.监听‘store.ts’中的‘模式’按键状态('true'/'false')
  - 1.状态为‘false’
    - 1.‘坐标’按键状态为‘false’

## 基于‘componetB1.tsx’容器布局

**1.布局结构说明(componetB1.tsx):**

- 1.排列方式:功能容器在‘componetB1’容器内垂直排列
- 2.排列结构:[featureB1_1]
            [featureB1_2]

**2.功能容器2(featureB1_2.tsx):**

- 1.容器样式:
  - 1.容器形状:长方形
  - 2.容器高度:继承父容器‘componetB1’的15%高度
  - 3.容器宽度:继承父容器'componetB1'的94%宽度
  - 4.背景颜色:#bebebe
  - 5.组件定位:相对定位
  - 6.溢出处理:隐藏
  - 7.顶部对齐:‘top:占‘componetB1’容器高的9%'
- 2.文本样式:
  - 1.默认文本:‘模式’
  - 2.字体大小:25px
  - 3.字体颜色:#242424
- 3.文本位置:
  - 1.位置:绝对位置
  - 2.顶部对齐:'top:featureB1_1高度的20%'
  - 3.左部对齐:'left:featureB1_1宽度的1%'

**3.按键调用(featureB1_2.tsx):**

- 1.注意:
  - 1.避免父容器属性裁剪下拉菜单显示
- 2.按键1组件调用:
  - 1.调用按键‘DropdownButton’
    - 1.配置参数:
      - 1.键高:占‘featureB1_2’容器高的35%
      - 2.键宽:占‘featureB1_2’容器宽的46%
    - 2.菜单按键:
      - 1.底色按键:
        - 1.按键模式:mode:button
        - 2.按键文本:'底色'
        - 3.文本大小:自适应
      - 2.彩色按键:
        - 1.按键模式:mode:button
        - 2.按键文本:'彩色'
        - 3.文本大小:自适应
      - 3.添加按键:
        - 1.按键模式:mode:add
        - 2.按键文本:无
        - 3.文本大小:无
  - 2.按键2组件调用:
    - 1.调用按键‘DropdownButton’
      - 1.配置参数:
        - 1.键高:占‘featureB1_2’容器高的35%
        - 2.键宽:占‘featureB1_2’容器宽的46%
      - 2.菜单按键:
        - 1.初始按键:
          - 1.按键模式:mode:button
          - 2.按键文本:'初始'
          - 3.文本大小:自适应
        - 2.坐标按键:
          - 1.按键模式:mode:button
          - 2.按键文本:'坐标'
          - 3.文本大小:自适应
        - 3.数字按键:
          - 1.按键模式:mode:button
          - 2.按键文本:'数字'
          - 3.文本大小:自适应

**4.按键布局(featureB1_2.tsx):**

- 1.按键1:
  - 1.位置:绝对位置
  - 2.顶部对齐:'top:featureB1_2高度的55%'
  - 3.左部对齐:'left:featureB1_2宽度的4%'
- 2.按键2:
  - 1.位置:绝对位置
  - 2.顶部对齐:'top:featureB1_2高度的55%'
  - 3.右部对齐:'right:featureB1_2宽度的4%'

**5.按键业务逻辑(store.ts):**

- 1.状态管理:
  - 1.按键1状态:
    - 1.'底色按键'的‘true/false’状态信息存储于‘store.ts’
    - 2.'彩色按键'的‘true/false’状态信息存储于‘store.ts’
    - 3.'添加按键'(一次性事件):
      - 1.向‘store.ts’或通过事件总线发出一个“添加”事件
      - 2.添加触发器状态
        - 1.更新时间戳
      - 3.触发对话框弹窗
        - 1.输入文本
          - 1.点击确认后记录输入文本
      - 4.存储按键的‘ture/false’状态信息存储于‘store.ts’
  - 2.按键2状态:
    - 1.'初始按键'的‘true/false’状态信息存储于‘store.ts’
    - 2.'坐标按键'的‘true/false’状态信息存储于‘store.ts’
    - 3.'数字按键'的‘true/false’状态信息存储于‘store.ts’
