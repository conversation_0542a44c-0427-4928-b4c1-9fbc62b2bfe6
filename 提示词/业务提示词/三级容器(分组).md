# 三级容器(分组)

## 基于‘compnetB1.tsx’容器布局

**1.布局结构说明(componetB1.tsx):**

- 1.排列方式:功能容器在‘componetB1’容器内垂直排列
- 2.排列结构:[featureB1_1]
            [featureB1_2]
            [featureB1_3]
- 3.容器间隔:通过[间隔]产生间隔
- 4.间隔:
  - 1.间隔属性:'margin-top: 容器‘componetB1’的3%高度'
  - 2.间隔对象:'featureB1_1'，'featureB1_2'，'featureB1_3'

**4.功能容器4(featureB1_3.tsx)：**

- 1.容器样式:
  - 1.容器形状:长方形
  - 2.容器高度:继承父容器‘componetB1’的15%高度
  - 3.容器宽度:继承父容器'componetB1'的94%宽度
  - 4.背景颜色:#bebebe
  - 5.展示方式:弹性布局
  - 6.弹性方向:水平
  - 7.对齐方式:水平，垂直居中
  - 8.溢出处理:隐藏
  - 9.组件间隔:顶部[间隔]
- 2.文本样式:
  - 1.默认文本:‘分组’
  - 2.字体大小:30px
  - 3.字体颜色:#242424
- 3.文本位置:
  - 1.位置:绝对位置
  - 2.顶部对齐:'top:featureB1_3高度的20%'
  - 3.左部对齐:'left:featureB1_3宽度的1%'
