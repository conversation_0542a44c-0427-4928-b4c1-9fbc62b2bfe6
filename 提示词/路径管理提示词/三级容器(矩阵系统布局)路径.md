# 三级容器(矩阵系统布局)

## 创建三级容器文件

**功能组件文件:**

- 1.创建功能容器1文件‘featureA1.tsx’ -> (根目录/frontend/features/feature)

**组件布局文件:**

- 1.创建组件布局文件‘featureA1_layout.tsx’ -> (根目录/frontend/features/logic)

**组件坐标文件:**

- 1.创建组件坐标计算文件‘featureA1_coordinate.tsx’-> (根目录/frontend/features/logic)

**组件底色文件:**

-1.创建组件底色计算文件‘featureA1_color.tsx’-> (根目录/frontend/features/logic)

**容器交互文件:**

- 1.创建功能容器交互文件‘logicA1.tsx’ -> (根目录/frontend/features/logic)
