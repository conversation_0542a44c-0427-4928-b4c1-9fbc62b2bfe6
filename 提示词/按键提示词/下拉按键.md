# 下拉按键

## 架构属性

**1.标准类型:**

- 1.React标准类型

## 下拉按键样式

**1.默认样式(style_dropdown.ts):**

- 1.按键样式:
  - 1.按键名称:dropdown
  - 2.按键形状:长方形
  - 3.按键高度:50px
  - 4.按键宽度:200px
  - 5.按键圆角:0px
  - 6.按键底色:#f1f1f1
  - 7.展示方式:弹性布局
  - 8.主轴对齐:水平居中
  - 9.交叉轴对齐:垂直居中
  - 10.鼠标指针样式:手型
- 2.下拉菜单样式:
  - 1.菜单形状:长方形
  - 2.菜单宽度:等于按键宽
  - 3.菜单最大高度:300px
  - 4.菜单滚动条:出现时显示
  - 5.菜单定位:绝对定位
  - 6.菜单隐藏:默认不显示
- 3.文字样式:
  - 1.默认文本:按键
  - 2.字体大小:20px
  - 3.文本颜色:#242424
  - 4.文本对齐:居中
-4.图标显示:
  - 1.图标类型:下拉箭头
  - 2.图标位置:
    - 1.右对齐:10px
  - 3.图标大小:20px
  - 4.图标颜色: #cccccc

## 菜单按键样式

**1.默认模式(style_dropdown.ts):**

- 1.默认按键(mode:button)
  - 1.键内文本
    - 1.文本颜色: #2d2d2d
    - 2.文本大小:15px
    - 3.对齐方式:水平居中

**2.新建模式(style_dropdown.ts):**

- 1.新建按键(mode:add)
  - 1.样式:
    - 1.图标类型:加号'+'图案，无底色
    - 2.图标大小:15px
    - 3.图标颜色: #cccccc
    - 4.布局方式:弹性布局
    - 5.对齐方式:水平居中

**3.存储按键(style_dropdown.ts):**

- 1.存储按键
  - 1.键内文本
    - 1.文本颜色: #2d2d2d
    - 2.文本大小:15px
    - 3.布局方式:弹性布局
    - 4.对齐方式:水平居中
  - 2.‘删除’图标
    - 1.图标
      - 1.图标类型:减号'-'图案，无底色
      - 2.图标颜色: #0d0d0d
      - 3.图标位置:
        - 1.右对齐:20px
      - 4.图标大小:15px

## 依赖组件样式

**1.对话框弹窗(style_dropdown.ts):**

- 1.说明:
  - 1.该组件由‘新建’模式(add)按键触发，其样式独立于下拉菜单

- 2.对话框样式:
  - 1.遮罩层样式:
    - 1.定位方式:固定定位
    - 2.尺寸:铺满整个视窗
    - 3.背景颜色: rgba(0, 0, 0, 0.7)
    - 4.层级:页面顶层
    - 5.展示方式:弹性布局
    - 6.主轴对齐:垂直,水平对齐
  - 2.对话框样式:
    - 1.背景颜色: #ffffff
    - 2.高度:250px
    - 3.宽度:400px
    - 4.圆角:10px
    - 5.层级:遮罩层之上
  - 3.框内布局:
    - 1.对齐方式:垂直，水平居中
    - 2.关闭图标
      - 1.图标样式:'x'图案，无底色
      - 2.图案颜色: #0d0d0d
      - 3.图案位置:
        - 1.右对齐:20px
      - 4.图案大小:40px
    - 3.输入框
      - 1.标签
        - 1.标签文本:名称
        - 2.字体大小:25px
        - 3.字体颜色: #9f9f9f
        - 4.与输入框间隔:5px
        - 5.对齐方式:
          - 1.左对齐:25px
      - 2.输入框
        - 1.输入框高:50px
        - 2.输入框宽:350px
        - 3.背景颜色: #ffffff
        - 4.输入边框:2px
        - 5.边框颜色: #9f9f9f
        - 6.边框圆角:5px
        - 7.文本颜色: #9f9f9f
        - 8.文本大小:20px
        - 9.对齐方式:左对齐:5px
        - 10.鼠标指针样式:文本
      - 3.错误信息
        - 1.显示位置:
          - 1.底部对齐:50px
        - 2.字体大小:20px
        - 3.字体颜色: #ff0000
        - 4.透明度:50%
        - 5.默认隐藏
        - 6.字体对齐:居中
        - 7.触发时长:显示3s
        - 8.定位方式:绝对定位
    - 4.确认按键:
      - 1.按键文本:确认
      - 2.文本颜色: #ffffff
      - 3.按键高度:30px
      - 4.按键宽度:80px
      - 5.按键底色: #0d0d0d
      - 6.按键位置:
        - 1.底部对齐:25px
        - 2.右部对齐:25px
      - 7.按键圆角:5px

## 下拉按键交互

**1.鼠标悬停/点击(event_dropdown.ts):**

- 1.菜单选项:
  - 1.鼠标悬停:
    - 1.默认模式(button)
      - 1.激活
        - 1.底色: #929292
        - 2.悬停: #b6b6b6
      - 2.未激活
        - 1.底色: #f1f1f1
        - 2.悬停: #dbdbdb
    - 2.新建模式(add)
      - 1.悬停底色: #dbdbdb
      - 2.鼠标按下: #929292
      - 3.鼠标松开: #f1f1f1
    - 3.对话框
      - 1.‘关闭’图标
        - 1.悬停颜色: #5e5e5e
      - 2.‘确认’按键
        - 1.悬停颜色: #5e5e5e
    - 4.存储按键
      - 1.激活
        - 1.底色: #929292
        - 2.悬停: #b6b6b6
      - 2.未激活
        - 1.底色: #f1f1f1
        - 2.悬停: #dbdbdb
      - 3.‘删除’图标(仅触发图标本身)
        - 1.悬停颜色: #5e5e5e
        - 2.悬停放大:2

**2.按键交互(event_dropdown.ts):**

- 1.点击下拉按键本身，展开/隐藏下拉菜单
- 2.点击菜单以外的任意地方，隐藏下拉菜单
- 4.对话框打开时，点击对话框区域不会隐藏下拉菜单
- 5.菜单选择规则
  - 1.选择模式为互斥单选,当一个选项被激活时，其他选项变为未激活状态
- 6.交互方式:
  - 1.'默认'模式(button)
    - 1.点击后，该选项变为“激活”状态
  - 2.'新建'模式(add)
    - 1.点击显示对话框
      - 1.输入名称验证
        - 1.报错: 不能为空、不能与现有名称重复
      - 2.点击‘确认’
        - 1.创建新'存储'按键菜单，并采用该名称对按键命名
        - 2.新菜单项位置: 添加到所有可选择项的末尾，但在‘新建’模式按键之上
        - 3.创建成功后，新创建的菜单项自动变为“激活”状态
      - 3.点击‘关闭’
        - 1.点击‘关闭’图标,关闭对话框
      - 4.'存储'按键
        - 1.点击选项主体，该选项变为“激活”状态
        - 2.点击'删除'图标
          - 1.从列表中移除该菜单项
          - 2.按键状态
            - 1.激活状态
              - 1.自动激活菜单中的首个按键
            - 2.非激活状态
              - 1.保持当前按键已激活状态
- 7.规则:
  - 1.‘默认’模式按键是基础按键，不提供删除功能
  - 2.‘新建’模式(add)按键的显示前提是：菜单栏中至少存在一个‘默认’模式(button)按键
  - 3.'新建'模式(add)按键永远位于下拉列表的最下方

## 按键组件

**1.创建普通按键组件(DropdownButton.tsx):**

- 1.组件应接收‘mode’属性来控制其行为和样式
- 2.组件内部需处理点击事件，并根据不同模式调用外部函数

**2.导出按键组件(index.ts):**

- 1.导出`DropdownButton`组件及相关的类型定义
