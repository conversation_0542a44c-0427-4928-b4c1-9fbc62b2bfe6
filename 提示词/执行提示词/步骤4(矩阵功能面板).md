# 矩阵功能面板

## 初始提示词

- 1.阅读‘前端技术栈.md’并用于构建代码
- 2.阅读‘三级容器(矩阵)路径.md’并用于创建文件
- 3.严格执行‘三级容器(矩阵).md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.检测代码能否正常运行
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤顺序执行矩阵功能面板的开发任务：

1. **技术栈准备**：首先阅读项目根目录下的'前端技术栈.md'文档，了解项目使用的前端技术栈（如React、TypeScript、样式框架等），并严格按照文档中规定的技术标准来编写代码
2. **文件结构规划**：阅读'三级容器(矩阵)路径.md'文档，了解矩阵功能相关文件的存放路径和目录结构，按照文档中的路径规范创建所需的文件和文件夹
3. **功能实现**：严格按照'三级容器(矩阵).md'文档中的详细指示和需求规范，实现矩阵功能面板的所有功能特性，不得偏离文档要求
4. **执行约束**：在完成上述步骤之前，严禁阅读任何其他文档或参考资料，只能依据当前提示词中明确提到的文档内容进行开发
5. **范围限制**：仅实现当前任务文档中明确描述的功能逻辑，不得自行添加额外功能或进行功能扩展
6. **质量保证**：完成代码编写后，进行代码测试验证，确保所有功能能够正常运行，没有语法错误或运行时错误
7. **项目维护**：根据新增的文件和依赖，相应更新项目根目录下的.gitignore文件，确保不必要的文件不被版本控制追踪
请严格按照1-7的顺序执行，每完成一个步骤后再进行下一步。
