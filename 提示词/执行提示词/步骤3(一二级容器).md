# 一二级容器

## 初始提示词

- 1.阅读‘前端技术栈.md’并用于构建代码
- 2.阅读‘一二级容器路径.md’并用于创建文件
- 3.严格执行‘一二级容器.md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.检测页面能否正常运行
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤严格执行前端容器组件的创建和配置：

1. **技术栈准备**：首先阅读项目根目录下的'前端技术栈.md'文档，了解项目使用的技术栈和开发规范，并将其作为代码构建的技术基础
2. **文件路径规划**：阅读'一二级容器路径.md'文档，获取准确的文件创建路径和目录结构信息，确保所有新建文件都放置在正确的位置
3. **核心任务执行**：严格按照'一二级容器.md'文档中的具体指示和要求执行开发任务，不得偏离或修改其中的规范
4. **文档阅读限制**：在执行过程中，除了上述三个指定的提示词文档外，禁止主动阅读项目中的其他文档或代码文件，除非任务执行过程中明确需要
5. **任务范围控制**：严格按照当前提示词文档提供的逻辑流程执行，不得自行补充额外的功能或步骤，确保任务范围的准确性
6. **功能验证测试**：完成代码开发后，通过启动开发服务器或其他适当方式验证页面能否正常加载和运行，确保功能的可用性
7. **项目配置更新**：根据新增的文件和依赖，相应更新项目根目录下的.gitignore文件，确保版本控制的正确配置
注意：请严格按照步骤顺序执行，每个步骤完成后再进行下一步。
