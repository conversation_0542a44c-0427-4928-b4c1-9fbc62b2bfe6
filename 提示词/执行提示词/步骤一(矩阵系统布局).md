# 矩阵系统

## 初始提示词

- 1.阅读‘前端技术栈.md’并用于构建代码
- 2.阅读‘三级容器(矩阵系统布局)路径.md’并用于创建文件
- 3.严格执行‘三级容器(矩阵系统布局).md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.检测代码功能是否正常运行
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤严格执行矩阵系统布局的实现：

1. **技术栈准备**：首先阅读项目根目录下的'前端技术栈.md'文档，了解项目使用的技术栈和开发规范，并将其作为后续代码构建的技术基础
2. **文件结构规划**：阅读'三级容器(矩阵系统布局)路径.md'文档，获取完整的文件和目录结构信息，用于创建所需的文件和文件夹
3. **核心实现**：严格按照'三级容器(矩阵系统布局).md'文档中的具体指示和要求进行开发，不得偏离文档中的设计规范和实现方案
4. **文档读取限制**：在执行过程中，除了上述三个指定文档外，禁止主动阅读其他任何项目文档，确保实现过程的专注性
5. **范围控制**：严格按照当前任务的逻辑流程执行，不得擅自添加额外功能或进行超出范围的补充开发
6. **功能验证**：完成代码实现后，进行功能测试以确保矩阵系统布局能够正常运行，包括但不限于组件渲染、布局响应和交互功能
7. **项目维护**：根据新增的文件和依赖，相应更新项目根目录下的.gitignore文件，确保版本控制的正确性

注意：请严格按照1-7的顺序执行，每完成一步后再进行下一步。
