# 下拉按键演示

## 初始提示词

- 1.阅读‘下拉按键演示路径.md’并用于创建文件
- 2.阅读‘前端技术栈.md’并用于构建代码
- 3.严格执行‘下拉按键演示.md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全

## 优化提示词

请按照以下步骤顺序执行下拉按键演示页面的开发任务：

1. **阅读路径配置文档**：首先阅读 `下拉按键演示路径.md` 文件，获取演示页面的文件路径和目录结构信息，用于后续文件创建和组织
2. **了解技术栈规范**：阅读 `前端技术栈.md` 文件，了解项目使用的前端技术栈和开发规范，确保演示页面代码实现符合项目标准
3. **执行核心开发指示**：严格按照 `下拉按键演示.md` 文件中的具体指示和要求进行演示页面开发，不得偏离或修改其中的逻辑和功能要求
4. **文档阅读限制**：在执行上述步骤时，严禁提前阅读除了上述指定的三个提示词文档以外的任何其他项目文档或代码文件
5. **功能范围限制**：仅实现上述文档中明确描述的演示页面功能逻辑流程，不得自行添加、扩展或补全任何未在文档中明确要求的功能特性
6. **版本控制更新**：完成演示页面开发后，如有需要，更新项目相关配置文件，确保新增的演示文件按照项目规范进行管理

注意：请严格按照步骤顺序执行，每个步骤完成后再进行下一步
