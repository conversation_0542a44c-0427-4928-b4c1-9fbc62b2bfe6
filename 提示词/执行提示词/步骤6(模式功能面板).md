# 模式功能面板

## 初始提示词

- 1.阅读‘前端技术栈.md’并用于构建代码
- 2.阅读‘三级容器(模式)路径.md’并用于创建文件
- 3.严格执行‘三级容器(模式).md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.检测代码能否正常运行
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤顺序执行模式功能面板的开发任务：

1. **技术栈准备**：首先阅读项目根目录下的'前端技术栈.md'文档，了解项目使用的前端技术栈和开发规范，并严格按照文档中的技术要求进行代码构建
2. **文件路径规划**：阅读'三级容器(模式)路径.md'文档，了解项目的文件组织结构和命名规范，按照文档中定义的路径结构创建相应的文件和文件夹
3. **功能实现**：严格按照'三级容器(模式).md'文档中的具体指示和要求实现模式功能面板，不得偏离文档中的规范和设计要求
4. **文档阅读限制**：在执行过程中，除了上述三个指定文档外，禁止提前阅读其他任何项目文档，确保按照既定流程进行开发
5. **范围控制**：严格按照当前任务的逻辑流程进行开发，不得自行添加额外功能或进行功能扩展，只完成明确要求的功能
6. **代码验证**：进行功能测试和代码检查，确保所有代码能够正常运行，没有语法错误或运行时错误，验证完成删除测试代码
7. **项目维护**：根据新增的文件和依赖，更新项目根目录下的.gitignore文件，确保不必要的文件不会被版本控制系统跟踪

注意：请严格按照1-7的顺序执行，每个步骤完成后再进行下一步。
