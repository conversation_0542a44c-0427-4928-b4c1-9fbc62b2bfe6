# 普通按键

## 初始提示词

- 1.阅读‘下拉按键路径.md’并用于创建文件
- 2.阅读‘前端技术栈.md’并用于构建代码
- 3.严格执行‘下拉按键.md’中的指示
- 4.阅读‘下拉按键演示.md’并用于创建演示文件
- 5.禁止提前阅读除提示词描述以外的文档
- 6.仅完成该文本提供的逻辑流程，禁止补全
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤顺序执行下拉按键组件的开发任务：

1. **阅读配置文档**：首先阅读 `下拉按键路径.md` 文件，获取文件路径和目录结构信息，用于后续文件创建
2. **了解技术栈**：阅读 `前端技术栈.md` 文件，了解项目使用的前端技术栈和开发规范，确保代码实现符合项目标准
3. **执行核心指示**：严格按照 `下拉按键.md` 文件中的具体指示和要求进行开发，不得偏离或修改其中的逻辑
4. **文档阅读限制**：在执行上述步骤时，严禁提前阅读除了上述指定提示词文档以外的任何其他项目文档或代码文件
5. **功能范围限制**：仅实现上述文档中明确描述的功能逻辑流程，不得自行添加、扩展或补全任何未在文档中明确要求的功能
6. **更新版本控制**：完成开发后，更新项目根目录下的 `.gitignore` 文件，确保新增的文件和目录按照项目规范进行版本控制管理

注意：请严格按照步骤顺序执行，每个步骤完成后再进行下一步
