# 项目文件创建

## 初始提示词

- 1.阅读‘项目文件路径.md’并用于创建文件夹
- 2.阅读‘前端技术栈.md’并安装依赖
- 3.禁止提前阅读除提示词描述以外的文档
- 4.仅完成该文本提供的逻辑流程，禁止补全
- 5.创建完成后检测文件夹和文件是否创建成功
- 6.更新.gitignore文档

## 优化提示词

请按照以下步骤严格执行前端项目文件结构的创建任务：

1. **读取项目结构文档**：阅读 `项目文件路径.md` 文件，获取完整的项目目录结构信息，并根据该文档中指定的路径创建所有必要的文件夹和文件
2. **安装技术栈依赖**：阅读 `前端技术栈.md` 文件，了解项目所需的技术栈和依赖包，使用适当的包管理器（npm/yarn/pnpm）安装所有必要的依赖项
3. **严格遵循文档范围**：在执行上述步骤时，严禁提前阅读或参考除了步骤1-2中明确指定的两个文档之外的任何其他文档，避免功能范围扩散
4. **限制任务范围**：仅完成本指令中明确列出的逻辑流程和任务，不得自行添加额外功能、优化或补全未明确要求的内容
5. **验证创建结果**：任务完成后，检查并确认所有指定的文件夹和文件是否已成功创建，验证目录结构是否与文档要求一致
6. **更新项目配置**：更新或创建 `.gitignore` 文件，确保包含适当的忽略规则（如 node_modules、构建产物、IDE配置文件等）
注意：请严格按照步骤顺序执行，每个步骤完成后再进行下一步。
