# 普通按键演示

## 演示文件路径

**1.演示文件夹:**

- 1.创建普通按键演示文件夹‘secondary-test’ -> (根目录/apps/frontend/ButtonCodes)
  - 1.创建普通按键演示文件‘SecondaryButton_test.tsx’

**2.创建按键演示文件(secondary-demo/page.tsx):**

- 1.创建普通按键演示页面文件夹‘secondary-demo’ -> (根目录/frontend/app)
  - 1.创建普通按键演示页面文件‘page.tsx’

## 测试内容

**1.普通按键演示(SecondaryButton_test):**

- 1.按键调用:
  - 1.调用按键'SecondaryButton'
    - 1.配置参数:
      - 1.键高:50px
      - 2.键宽:200px
      - 3.模式:mode:toggle
    - 2.文本:
      - 1.键内文本:'持续状态'
      - 2.文本大小:尺寸自适应
  - 2.调用按键'SecondaryButton'
    - 1.配置参数:
      - 1.键高:50px
      - 2.键宽:200px
      - 3.模式:mode:instant
    - 2.文本:
      - 1.键内文本:'瞬时状态'
      - 2.文本大小:尺寸自适应

## 演示页面

**1.要求:**

- 1.仅显示按键功能，保持页面整洁
