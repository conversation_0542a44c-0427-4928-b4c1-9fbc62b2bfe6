# 下拉按键演示

## 演示文件路径

**1.演示文件夹:**

- 1.创建下拉按键演示文件夹‘dropdown-test’ -> (根目录/apps/frontend/ButtonCodes)
  - 1.创建下拉按键演示文件‘DropdownButton_test.tsx’
  
**2.创建按键演示文件(secondary-demo/page.tsx):**

- 1.创建下拉按键演示页面文件夹‘dropdown-demo’ -> (根目录/frontend/app)
  - 1.创建普通按键演示页面文件‘page.tsx’

## 测试内容

**1.下拉按键演示(DropdownButton_test):**

- 1.按键组件调用:
  - 1.业务按键:
    - 1.调用按键‘DropdownButton’
      - 1.配置参数:
        - 1.键高:50px
        - 2.键宽:200px
      - 2.文本:
        - 1.文本大小:尺寸自适应
      - 3.菜单:
        - 1.菜单按键1:
          - 1.按键模式:mode:button
          - 2.按键文本:'业务1'
          - 3.文本大小:尺寸自适应
        - 2.菜单按键2:
          - 1.按键模式:mode:button
          - 2.按键文本:'业务2'
          - 3.文本大小:尺寸自适应
        - 3.菜单按键3:
          - 1.按键模式:mode:add
          - 2.按键文本:无
          - 3.文本大小:无

## 演示页面

**1.要求:**

- 1.仅显示按键，保持页面整洁
- 2.去除所有信息说明
